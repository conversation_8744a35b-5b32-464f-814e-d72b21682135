﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui;

public class SearchService : ISearchService
{
    private readonly ILogger<SearchService> _logger;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IReferenceRepository _referenceRepository;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly IClassificationCategoryService _classificationCategoryService;
    private readonly IPotentialCaseAdditionalFieldService _potentialCaseAdditionalFieldService;
    private readonly ICompanyRepository _companyRepository;

    public SearchService(
        ILoggerFactory loggerFactory,
        IReferenceClassificationRepository referenceClassificationRepository,
        IReferenceRepository referenceRepository,
        ISubstanceRepository substanceRepository,
        IClassificationCategoryService classificationCategoryService,
        IPotentialCaseAdditionalFieldService potentialCaseAdditionalFieldService,
        ICompanyRepository companyRepository)
    {
        _logger = loggerFactory.CreateLogger<SearchService>();
        _referenceClassificationRepository = referenceClassificationRepository;
        _referenceRepository = referenceRepository;
        _substanceRepository = substanceRepository;
        _classificationCategoryService = classificationCategoryService;
        _potentialCaseAdditionalFieldService = potentialCaseAdditionalFieldService;
        _companyRepository = companyRepository;
    }

    public async Task<ReferenceClassificationSearchResultsModel> ClassificationSearch(ClassificationSearchRequest request, User user, int? maxRows)
    {
        try
        {
            var searchResults = await _referenceClassificationRepository.Search(request, user, maxRows);

            return new ReferenceClassificationSearchResultsModel()
            {
                Classifications = searchResults
            };
        }
        catch (SqlException e) when (e.Number == 7630)
        {
            _logger.LogWarning(e, "Full-text search syntax error for user {UserId} with message: {Message}", user.Id, e.Message);

            return new ReferenceClassificationSearchResultsModel()
            {
                FullTextErrorMessage = e.Message
            };
        }
    }

#pragma warning disable S2139 // "Either log this exception and handle it, or rethrow it with some contextual information" - figure out what exception we should be throwing as we're catching every exception

    public async Task<IEnumerable<ReferenceSupportModel>> ReferenceSearch(string term, User user)
    {
        try
        {
            return await _referenceRepository.Search(term, user);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error searching references for term: '{Term}'", LogSanitizer.Sanitize(term));
            throw;
        }
    }

#pragma warning restore

    public async Task<PrintPreviewPageModel> PrintPreview(ClassificationSearchRequest request, User user, int? maxRows)
    {
        var filters = await GetPrintPreviewFilters(request);
        var results = await _referenceClassificationRepository.PrintPreview(request, user, maxRows);

        return new PrintPreviewPageModel
        {
            Filters = filters,
            Classifications = results
        };
    }

    private async Task<PrintPreviewFiltersModel> GetPrintPreviewFilters(ClassificationSearchRequest request)
    {
        return new PrintPreviewFiltersModel()
        {
            Term = request.Term,
            CreatedFrom = request.CreatedFrom,
            CreatedTo = request.CreatedTo,
            LastUpdatedFrom = request.LastUpdatedFrom,
            LastUpdatedTo = request.LastUpdatedTo,
            Substances = request.Substances.SelectedIds.Any() ? (await _substanceRepository.GetAllAsync()).Where(x => request.Substances.SelectedIds.Contains(x.Id)).Select(x => x.Name).ToList() : new List<string>(),
            Categories = request.ClassificationCategories.SelectedIds.Any() ? (await _classificationCategoryService.GetAllAsync()).Where(x => request.ClassificationCategories.SelectedIds.Contains(x.Id)).Select(x => x.Name).ToList() : new List<string>(),
            SpecialSituations = request.SpecialSituations.SelectedIds.Any() ? (await _potentialCaseAdditionalFieldService.GetAllAsync()).Where(x => request.SpecialSituations.SelectedIds.Contains(x.Id)).Select(x => x.Name).ToList() : new List<string>(),
            PSUR = request.PSUR,
            Company = request.CompanyId > 0 ? (await _companyRepository.GetByIdAsync(request.CompanyId))?.Name : "",
            Title = request.Title,
            MeshTerms = request.MeshTerm,
            MeshTermsOr = request.SearchMeshTermWithOr,
            Keywords = request.Keyword,
            KeywordsOr = request.SearchKeywordWithOr
        };
    }
}
