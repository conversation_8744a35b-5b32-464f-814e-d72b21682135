using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;
using Xunit;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests;

public class ApifyNotificationSimplifiedTests
{
    private readonly IApifyNotification _notificationService;
    private readonly Mock<ILogger<ApifyNotification>> _mockLogger = new();
    private readonly Mock<IApifyClient> _apifyClient = new();
    private readonly Mock<IDownloadBlobStorage> _blobStorage = new();
    private readonly Mock<IDataExtractionClient> _client = new();
    private readonly Mock<IDocumentService> _mockDocumentService = new();
    private readonly Mock<BlobClient> _mockBlobClient = new();

    public ApifyNotificationSimplifiedTests()
    {
        _notificationService = new ApifyNotification(_apifyClient.Object, _blobStorage.Object, _client.Object, _mockLogger.Object);

        // Setup mock for WriteDataItemAsync test
        _mockDocumentService.Setup(x => x.Create(
            It.IsAny<DocumentDescriptor>(),
            It.IsAny<Stream>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(_mockBlobClient.Object);
    }

    [Fact]
    public async Task RunSucceeded_ShouldCallTransferFilesAsyncWithMetadata()
    {
        // Arrange
        var actorTaskId = "test-task-123";
        var payload = new ApifyWebhookPayload
        {
            resource = new Resource
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = "dataset-123",
                defaultKeyValueStoreId = "store-123"
            }
        };

        var mockFilePaths = new List<string> { "scraping/test-task-123/file1.pdf", "scraping/test-task-123/file2.pdf" };
        var mockMetadata = new Dictionary<string, string>
        {
            ["JournalId"] = "1",
            ["JournalName"] = "Nature",
            ["JournalMatched"] = "true"
        };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(actorTaskId, It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockFilePaths);

        _blobStorage.Setup(x => x.GetBlobMetadataAsync(actorTaskId, It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockMetadata);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
              .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Once());

        // Verify TransferFilesAsync was called with the wrapper (not the original storage)
        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                payload.resource.defaultDatasetId,
                payload.resource.defaultKeyValueStoreId,
                It.IsAny<IDownloadBlobStorage>(), // Now it's called with the wrapper
                It.IsNotNull<IReadOnlyDictionary<string, string>>(),
                CancellationToken.None
            ),
            Times.Once()
        );

        // Verify ExtractDataCommand was sent for each file
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Exactly(2));
    }

    [Fact]
    public async Task RunSucceeded_ShouldHandleEmptyFileList()
    {
        // Arrange
        var actorTaskId = "test-task-empty";
        var payload = new ApifyWebhookPayload
        {
            resource = new Resource
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = "dataset-123",
                defaultKeyValueStoreId = "store-123"
            }
        };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(actorTaskId, It.IsAny<CancellationToken>()))
                   .ReturnsAsync(new List<string>());

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Once());
        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<IDownloadBlobStorage>(),
                It.IsAny<IReadOnlyDictionary<string, string>>(),
                It.IsAny<CancellationToken>()
            ),
            Times.Once()
        );

        // No ExtractDataCommand should be sent for empty file list
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Never);
    }

    [Fact]
    public async Task RunSucceeded_WithJournalDetection_ShouldGroupFilesByJournal()
    {
        // Arrange
        var actorTaskId = "nature-scraping-task";
        var payload = new ApifyWebhookPayload
        {
            resource = new Resource
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = "dataset-123",
                defaultKeyValueStoreId = "store-123"
            }
        };

        var mockFilePaths = new List<string>
        {
            "scraping/nature-scraping-task/nature_article1.pdf",
            "scraping/nature-scraping-task/nature_article2.pdf"
        };

        // Mock metadata that would trigger journal detection
        var mockMetadata = new Dictionary<string, string>
        {
            ["url"] = "https://nature.com/articles/article1",
            ["TaskId"] = actorTaskId,
            ["Source"] = "Apify"
        };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(actorTaskId, It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockFilePaths);

        _blobStorage.Setup(x => x.GetBlobMetadataAsync(actorTaskId, It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockMetadata);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
              .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Once());

        // Verify TransferFilesAsync was called with the wrapper
        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                payload.resource.defaultDatasetId,
                payload.resource.defaultKeyValueStoreId,
                It.IsAny<IDownloadBlobStorage>(), // Called with wrapper
                It.IsNotNull<IReadOnlyDictionary<string, string>>(),
                CancellationToken.None
            ),
            Times.Once()
        );

        // Verify ExtractDataCommand was sent with journal information
        _client.Verify(x => x.Send(It.Is<ExtractDataCommand>(cmd =>
            cmd.JournalId == 1 &&
            cmd.JournalName == "Nature" &&
            cmd.SourceUrl == "https://nature.com")), Times.Exactly(2));
    }

    [Fact]
    public async Task RunSucceeded_WithWrapper_EnsuresWriteDataItemAsyncIsCalled()
    {
        // Arrange
        var actorTaskId = "nature-scraping-task";
        var payload = new ApifyWebhookPayload
        {
            resource = new Resource
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = "dataset-123",
                defaultKeyValueStoreId = "store-123"
            }
        };

        var mockFilePaths = new List<string>
        {
            "scraping/nature-scraping-task/nature_article1.pdf"
        };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(actorTaskId, It.IsAny<CancellationToken>()))
                   .ReturnsAsync(mockFilePaths);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
              .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Once());

        // ✅ KEY ASSERTION: Verify that TransferFilesAsync was called with a wrapper
        // This ensures that WriteDataItemAsync will be called through the wrapper
        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                payload.resource.defaultDatasetId,
                payload.resource.defaultKeyValueStoreId,
                It.IsAny<IDownloadBlobStorage>(), // This should be the wrapper, not the original storage
                It.IsNotNull<IReadOnlyDictionary<string, string>>(),
                CancellationToken.None
            ),
            Times.Once()
        );

        // Verify ExtractDataCommand was sent
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Once);
    }
}
