﻿using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui.Unit.Tests;

public class PrintPreviewPageModelTests
{
    [Fact]
    public void SetSpecialSituations_sets_list_of_potential_case_additional_information_for_bracketed_list()
    {
        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Potential Case", PotentialCaseAdditionalInformation = "[1][3][5]"}
            }
        };

        // Act
        printPreviewPageModel.SetSpecialSituations("Potential Case", GetPotentialCaseAdditionalFields());

        // Assert
        Assert.Equal(3, printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList.Count);

        Assert.Equal("Field 1", printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList[0]);
        Assert.Equal("Field 3", printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList[1]);
        Assert.Equal("Field 5", printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList[2]);
    }

    [Fact]
    public void SetSpecialSituations_sets_empty_list_of_potential_case_additional_information_for_non_potential_case()
    {
        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Safety relevant information", PotentialCaseAdditionalInformation = null}
            }
        };

        // Act
        printPreviewPageModel.SetSpecialSituations("Potential Case", GetPotentialCaseAdditionalFields());

        // Assert
        Assert.Empty(printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList);
    }

    [Fact]
    public void SetSpecialSituations_sets_empty_list_of_potential_case_additional_information_for_empty_list()
    {
        // Arrange
        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Potential Case", PotentialCaseAdditionalInformation = null}
            }
        };

        // Act
        printPreviewPageModel.SetSpecialSituations("Potential Case", GetPotentialCaseAdditionalFields());

        // Assert
        Assert.Empty(printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList);
    }

    private ICollection<PotentialCaseAdditionalFieldModel> GetPotentialCaseAdditionalFields()
    {
        return
        [
            new PotentialCaseAdditionalFieldModel { Id = 1, Name = "Field 1" },
            new PotentialCaseAdditionalFieldModel { Id = 2, Name = "Field 2" },
            new PotentialCaseAdditionalFieldModel { Id = 3, Name = "Field 3" },
            new PotentialCaseAdditionalFieldModel { Id = 4, Name = "Field 4" },
            new PotentialCaseAdditionalFieldModel { Id = 5, Name = "Field 5" }
        ];
    }
}