﻿using Azure.Storage.Blobs.Models;
using CommunityToolkit.Diagnostics;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Import;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service;

public class DownloadBlobStorage : IDownloadBlobStorage
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageImportScrapeDocumentOptions _importScrapeDocumentOptions;
    private readonly IBlobContainerClientProvider _blobContainerClientProvider;
    private string _folderName;

    public DownloadBlobStorage(
        IDocumentService documentService,
        IOptions<AzureStorageImportScrapeDocumentOptions> importScrapeDocumentOptions,
        IBlobContainerClientProvider blobContainerClientProvider)
    {
        _documentService = documentService;
        _folderName = string.Empty;
        _importScrapeDocumentOptions = importScrapeDocumentOptions.Value;
        _blobContainerClientProvider = blobContainerClientProvider;
    }

    public void SetBlobFolderName(string folderName)
    {
        _folderName = folderName;
    }

    public async Task<IEnumerable<string>> GetBlobPathsAsync(string folderName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(folderName))
            throw new ArgumentNullException(nameof(folderName));

        var documentDescriptor = GetDocumentDescriptor(folderName);
        var containerClient = _blobContainerClientProvider.Provide(documentDescriptor);
        var prefix = $"scraping/{folderName}/";

        var blobPaths = new List<string>();

        await foreach (BlobItem blobItem in containerClient.GetBlobsAsync(
            prefix: prefix,
            cancellationToken: cancellationToken))
        {
            // Get the full blob path (e.g., "scraping/myfolder/file1.pdf")
            blobPaths.Add(blobItem.Name);
        }

        return blobPaths;
    }

    public async Task<Dictionary<string, string>?> GetBlobMetadataAsync(string folderName, string fileName, CancellationToken cancellationToken = default)
    {
        Guard.IsNotNullOrEmpty(folderName);
        Guard.IsNotNullOrEmpty(fileName);

        try
        {
            var documentDescriptor = GetDocumentDescriptor(fileName);

            var containerClient = _blobContainerClientProvider.Provide(documentDescriptor);
            var blobClient = containerClient.GetBlobClient($"scraping/{folderName}/{fileName}");

            if (!await blobClient.ExistsAsync(cancellationToken))
            {
                return null;
            }

            var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);
            return properties.Value.Metadata.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        catch (Exception)
        {
            // Log exception if needed
            return null;
        }
    }

    private DocumentDescriptor GetDocumentDescriptor(string fileName)
    {
        var blobName = $"scraping/{_folderName}/{fileName}";
        return new DocumentDescriptor(_importScrapeDocumentOptions.AccountName, _importScrapeDocumentOptions.ContainerName, blobName);
    }

    public async Task WriteDataItemAsync(string fileName, Stream fileStream, IReadOnlyDictionary<string, string>? metadata = null, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(fileName);

        // Store the file with enhanced metadata including journal information
        var blobClient = await _documentService.Create(documentDescriptor, fileStream, cancellationToken);

        // ✅ IMPLEMENT JOURNAL DETECTION HERE - This is where we enhance metadata with journal info
        var enhancedMetadata = EnhanceMetadataWithJournalInfo(fileName, metadata);

        // Set the enhanced metadata on the blob
        if (enhancedMetadata != null && enhancedMetadata.Count > 0)
        {
            try
            {
                await blobClient.SetMetadataAsync(enhancedMetadata, cancellationToken: cancellationToken);
                System.Diagnostics.Debug.WriteLine($"✅ Set metadata for {fileName} with {enhancedMetadata.Count} properties");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Failed to set metadata for {fileName}: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// ✅ JOURNAL DETECTION IMPLEMENTATION - Enhances metadata with journal information
    /// </summary>
    private Dictionary<string, string> EnhanceMetadataWithJournalInfo(string fileName, IReadOnlyDictionary<string, string>? originalMetadata)
    {
        var metadata = originalMetadata?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>();

        // Add basic file metadata
        metadata["FileName"] = fileName;
        metadata["ProcessedAt"] = DateTime.UtcNow.ToString("O");
        metadata["FolderName"] = _folderName;

        // ✅ IMPLEMENT JOURNAL DETECTION STRATEGIES
        var journalInfo = DetermineJournalInfo(metadata, fileName);
        if (journalInfo != null)
        {
            metadata["JournalId"] = journalInfo.JournalId.ToString();
            metadata["JournalName"] = journalInfo.JournalName;
            metadata["JournalSourceUrl"] = journalInfo.SourceUrl;
            metadata["JournalMatched"] = "true";
            metadata["JournalMatchStrategy"] = journalInfo.MatchStrategy;

            System.Diagnostics.Debug.WriteLine($"✅ Matched file '{fileName}' to journal: {journalInfo.JournalName} (ID: {journalInfo.JournalId}) via {journalInfo.MatchStrategy}");
        }
        else
        {
            metadata["JournalMatched"] = "false";
            System.Diagnostics.Debug.WriteLine($"❌ Could not determine journal for file '{fileName}' in task {_folderName}");
        }

        return metadata;
    }

    private JournalInfo? DetermineJournalInfo(Dictionary<string, string> metadata, string fileName)
    {
        try
        {
            // Strategy 1: Check if we have a source URL in the metadata from Apify
            if (metadata.TryGetValue("url", out var sourceUrl) && !string.IsNullOrEmpty(sourceUrl))
            {
                var journalInfo = ExtractJournalInfoFromUrl(sourceUrl);
                if (journalInfo.HasValue)
                {
                    return new JournalInfo
                    {
                        JournalId = journalInfo.Value.id,
                        JournalName = journalInfo.Value.name,
                        SourceUrl = journalInfo.Value.url,
                        MatchStrategy = "url_domain"
                    };
                }
            }

            // Strategy 2: Check for other Apify metadata fields that might contain source info
            if (metadata.TryGetValue("sourceUrl", out var altSourceUrl) && !string.IsNullOrEmpty(altSourceUrl))
            {
                var journalInfo = ExtractJournalInfoFromUrl(altSourceUrl);
                if (journalInfo.HasValue)
                {
                    return new JournalInfo
                    {
                        JournalId = journalInfo.Value.id,
                        JournalName = journalInfo.Value.name,
                        SourceUrl = journalInfo.Value.url,
                        MatchStrategy = "source_url"
                    };
                }
            }

            // Strategy 3: Try to infer from file name patterns
            var journalFromFileName = GetJournalFromPattern(fileName.ToLowerInvariant());
            if (journalFromFileName != null)
            {
                journalFromFileName.MatchStrategy = "filename_pattern";
                return journalFromFileName;
            }

            // Strategy 4: Check task folder name for journal hints
            var journalFromTask = GetJournalFromPattern(_folderName.ToLowerInvariant());
            if (journalFromTask != null)
            {
                journalFromTask.MatchStrategy = "task_pattern";
                return journalFromTask;
            }

            return null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"⚠️ Error determining journal metadata for file '{fileName}': {ex.Message}");
            return null;
        }
    }

    private (int id, string name, string url)? ExtractJournalInfoFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            var domain = uri.Host.ToLowerInvariant();

            return domain switch
            {
                var d when d.Contains("nature.com") => (1, "Nature", "https://nature.com"),
                var d when d.Contains("science.org") => (2, "Science", "https://science.org"),
                var d when d.Contains("nejm.org") => (3, "New England Journal of Medicine", "https://nejm.org"),
                var d when d.Contains("thelancet.com") => (4, "The Lancet", "https://thelancet.com"),
                var d when d.Contains("bmj.com") => (5, "BMJ", "https://bmj.com"),
                var d when d.Contains("jama.jamanetwork.com") => (6, "JAMA", "https://jama.jamanetwork.com"),
                _ => null
            };
        }
        catch
        {
            return null;
        }
    }

    private JournalInfo? GetJournalFromPattern(string text)
    {
        return text switch
        {
            var t when t.Contains("nature") => new JournalInfo { JournalId = 1, JournalName = "Nature", SourceUrl = "https://nature.com" },
            var t when t.Contains("science") => new JournalInfo { JournalId = 2, JournalName = "Science", SourceUrl = "https://science.org" },
            var t when t.Contains("nejm") => new JournalInfo { JournalId = 3, JournalName = "New England Journal of Medicine", SourceUrl = "https://nejm.org" },
            var t when t.Contains("lancet") => new JournalInfo { JournalId = 4, JournalName = "The Lancet", SourceUrl = "https://thelancet.com" },
            var t when t.Contains("bmj") => new JournalInfo { JournalId = 5, JournalName = "BMJ", SourceUrl = "https://bmj.com" },
            var t when t.Contains("jama") => new JournalInfo { JournalId = 6, JournalName = "JAMA", SourceUrl = "https://jama.jamanetwork.com" },
            _ => null
        };
    }

    private class JournalInfo
    {
        public int JournalId { get; set; }
        public string JournalName { get; set; } = string.Empty;
        public string SourceUrl { get; set; } = string.Empty;
        public string MatchStrategy { get; set; } = string.Empty;
    }
}
