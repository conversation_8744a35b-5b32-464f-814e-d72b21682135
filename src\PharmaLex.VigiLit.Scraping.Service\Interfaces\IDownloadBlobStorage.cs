﻿using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

public interface IDownloadBlobStorage : IDownloadStorage
{
    void SetBlobFolderName(string folderName);
    Task<IEnumerable<string>> GetBlobPathsAsync(string folderName, CancellationToken cancellationToken = default);
    Task<Dictionary<string, string>?> GetBlobMetadataAsync(string folderName, string fileName, CancellationToken cancellationToken = default);
}
